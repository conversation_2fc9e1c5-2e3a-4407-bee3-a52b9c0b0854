import React from 'react';
import { Select, SelectItem, Avatar } from '@heroui/react';
import { useTranslation } from 'react-i18next';
import { supportedLanguages } from '../i18n';
import type { SupportedLanguage } from '../i18n/types';

interface LanguageSelectorProps {
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ className }) => {
  const { i18n, t } = useTranslation('settings');

  const handleLanguageChange = (keys: any) => {
    const selectedLanguage = Array.from(keys)[0] as SupportedLanguage;
    if (selectedLanguage && selectedLanguage !== i18n.language) {
      i18n.changeLanguage(selectedLanguage);
    }
  };

  const currentLanguage = supportedLanguages.find(lang => lang.code === i18n.language) || supportedLanguages[0];

  return (
    <Select
      label={t('account.language')}
      description={t('account.language_description')}
      placeholder="选择语言"
      selectedKeys={[i18n.language]}
      onSelectionChange={handleLanguageChange}
      className={className}
      startContent={
        <span className="text-lg">{currentLanguage.flag}</span>
      }
    >
      {supportedLanguages.map((language) => (
        <SelectItem
          key={language.code}
          value={language.code}
          startContent={
            <Avatar
              alt={language.name}
              className="w-6 h-6"
              src={`data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'><text x='50%' y='50%' text-anchor='middle' dy='0.3em' font-size='16'>${language.flag}</text></svg>`}
            />
          }
        >
          {language.name}
        </SelectItem>
      ))}
    </Select>
  );
};

export default LanguageSelector;
