"use client";

import * as React from "react";
import { Button, Input, Select, SelectItem, Spacer } from "@heroui/react";
import { cn } from "@heroui/react";
import { useTranslation } from "react-i18next";
import LanguageSelector from "../LanguageSelector";

interface AccountSettingCardProps {
  className?: string;
}

const timeZoneOptions = [
  {
    label: "Coordinated Universal Time (UTC-3)",
    value: "utc-3",
    description: "Coordinated Universal Time (UTC-3)",
  },
  {
    label: "Coordinated Universal Time (UTC-4)",
    value: "utc-4",
    description: "Coordinated Universal Time (UTC-4)",
  },
  {
    label: "Coordinated Universal Time (UTC-5)",
    value: "utc-5",
    description: "Coordinated Universal Time (UTC-5)",
  },
];

const AccountSetting = React.forwardRef<HTMLDivElement, AccountSettingCardProps>(
  ({ className, ...props }, ref) => {
    const { t } = useTranslation('settings');

    return (
      <div ref={ref} className={cn("p-2", className)} {...props}>
        {/* Full name */}
        <div>
          <p className="text-base font-medium text-default-700">{t('account.full_name')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">{t('account.full_name_description')}</p>
          <Input className="mt-2" placeholder={t('profile.full_name_placeholder')} />
        </div>
        <Spacer y={2} />
        {/* Username */}
        <div>
          <p className="text-base font-medium text-default-700">{t('account.username')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">{t('account.username_description')}</p>
          <Input className="mt-2" placeholder={t('profile.username_placeholder')} />
        </div>
        <Spacer y={2} />
        {/* Email Address */}
        <div>
          <p className="text-base font-medium text-default-700">{t('account.email')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">
            {t('account.email_description')}
          </p>
          <Input className="mt-2" placeholder={t('profile.email_placeholder')} />
        </div>
        <Spacer y={2} />
        {/* Language */}
        <section>
          <LanguageSelector className="mt-2" />
        </section>
        <Spacer y={2} />
        {/* Timezone */}
        <section>
          <div>
            <p className="text-base font-medium text-default-700">{t('account.timezone')}</p>
            <p className="mt-1 text-sm font-normal text-default-400">{t('account.timezone_description')}</p>
          </div>
          <Select className="mt-2" defaultSelectedKeys={["utc-3"]}>
            {timeZoneOptions.map((timeZoneOption) => (
              <SelectItem key={timeZoneOption.value} value={timeZoneOption.value}>
                {timeZoneOption.label}
              </SelectItem>
            ))}
          </Select>
        </section>
        <Spacer y={2} />
        <Button className="mt-4 bg-default-foreground text-background" size="sm">
          {t('common:common.save')}
        </Button>
      </div>
    );
  },
);

AccountSetting.displayName = "AccountSetting";

export default AccountSetting;
