"use client";

import * as React from "react";
import { Card, CardBody, Avatar, Button, Badge, Input, Spacer, Textarea } from "@heroui/react";
import { Icon } from "@iconify/react";
import { cn } from "@heroui/react";
import { useTranslation } from "react-i18next";

interface ProfileSettingCardProps {
  className?: string;
}

const ProfileSetting = React.forwardRef<HTMLDivElement, ProfileSettingCardProps>(
  ({ className, ...props }, ref) => {
    const { t } = useTranslation('settings');

    return (
      <div ref={ref} className={cn("p-2", className)} {...props}>
        {/* Profile */}
        <div>
          <p className="text-base font-medium text-default-700">{t('profile.title')}</p>
          <p className="mt-1 text-sm font-normal text-default-400">
            {t('profile.subtitle')}
          </p>
        <Card className="mt-4 bg-default-100" shadow="none">
          <CardBody>
            <div className="flex items-center gap-4">
              <Badge
                showOutline
                classNames={{
                  badge: "w-5 h-5",
                }}
                content={
                  <Button
                    isIconOnly
                    className="h-5 w-5 min-w-5 bg-background p-0 text-default-500"
                    radius="full"
                    size="sm"
                    variant="bordered"
                  >
                    <Icon className="h-[9px] w-[9px]" icon="solar:pen-linear" />
                  </Button>
                }
                placement="bottom-right"
                shape="circle"
              >
                <Avatar
                  className="h-16 w-16"
                  src="https://nextuipro.nyc3.cdn.digitaloceanspaces.com/components-images/avatars/e1b8ec120710c09589a12c0004f85825.jpg"
                />
              </Badge>
              <div>
                <p className="text-sm font-medium text-default-600">Kate Moore</p>
                <p className="text-xs text-default-400">Customer Support</p>
                <p className="mt-1 text-xs text-default-400"><EMAIL></p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
      <Spacer y={4} />
      {/* Title */}
      <div>
        <p className="text-base font-medium text-default-700">{t('profile.title_field')}</p>
        <p className="mt-1 text-sm font-normal text-default-400">{t('profile.title_description')}</p>
        <Input className="mt-2" placeholder={t('profile.title_placeholder')} />
      </div>
      <Spacer y={2} />
      {/* Location */}
      <div>
        <p className="text-base font-medium text-default-700">{t('profile.location')}</p>
        <p className="mt-1 text-sm font-normal text-default-400">{t('profile.location_description')}</p>
        <Input className="mt-2" placeholder={t('profile.location_placeholder')} />
      </div>
      <Spacer y={4} />
      {/* Biography */}
      <div>
        <p className="text-base font-medium text-default-700">{t('profile.biography')}</p>
        <p className="mt-1 text-sm font-normal text-default-400">
          {t('profile.biography_description')}
        </p>
        <Textarea
          className="mt-2"
          classNames={{
            input: cn("min-h-[115px]"),
          }}
          placeholder={t('profile.biography_placeholder')}
        />
      </div>
      <Button className="mt-4 bg-default-foreground text-background" size="sm">
        {t('profile.update_profile')}
      </Button>
      </div>
    );
  }
);

ProfileSetting.displayName = "ProfileSetting";

export default ProfileSetting;
