import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { HeroUIProvider } from '@heroui/react';
import SettingsPage from '../SettingsPage';

// Mock wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <HeroUIProvider>
    {children}
  </HeroUIProvider>
);

describe('SettingsPage', () => {
  it('renders settings page with title', () => {
    render(
      <TestWrapper>
        <SettingsPage />
      </TestWrapper>
    );

    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Customize settings, email preferences, and web appearance.')).toBeInTheDocument();
  });

  it('renders all setting tabs', () => {
    render(
      <TestWrapper>
        <SettingsPage />
      </TestWrapper>
    );

    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Appearance')).toBeInTheDocument();
    expect(screen.getByText('Account')).toBeInTheDocument();
    expect(screen.getByText('Billing')).toBeInTheDocument();
    expect(screen.getByText('Team')).toBeInTheDocument();
  });

  it('renders profile setting by default', () => {
    render(
      <TestWrapper>
        <SettingsPage />
      </TestWrapper>
    );

    // Profile tab should be active by default and show profile content
    expect(screen.getByText('This displays your public profile on the site.')).toBeInTheDocument();
    expect(screen.getByText('Update Profile')).toBeInTheDocument();
  });
});
