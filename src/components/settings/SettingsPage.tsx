"use client";

import React from "react";
import { Tab, Tabs } from "@heroui/react";
import { useTranslation } from "react-i18next";

import ProfileSetting from "./ProfileSetting";
import AppearanceSetting from "./AppearanceSetting";
import AccountSetting from "./AccountSetting";
import BillingSetting from "./BillingSetting";
import TeamSetting from "./TeamSetting";

export default function SettingsPage() {
  const { t } = useTranslation('settings');

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {/* Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold leading-9 text-default-foreground">{t('title')}</h1>
        <h2 className="mt-2 text-small text-default-500">
          {t('subtitle')}
        </h2>
      </div>

      {/* Tabs */}
      <Tabs
        fullWidth
        classNames={{
          base: "mt-6",
          cursor: "bg-content1 dark:bg-content1",
          panel: "w-full p-0 pt-4",
        }}
      >
        <Tab key="profile" title={t('tabs.profile')}>
          <ProfileSetting />
        </Tab>
        <Tab key="appearance" title={t('tabs.appearance')}>
          <AppearanceSetting />
        </Tab>
        <Tab key="account" title={t('tabs.account')}>
          <AccountSetting />
        </Tab>
        <Tab key="billing" title={t('tabs.billing')}>
          <BillingSetting />
        </Tab>
        <Tab key="team" title={t('tabs.team')}>
          <TeamSetting />
        </Tab>
      </Tabs>
    </div>
  );
}
