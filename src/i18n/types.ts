// 翻译键的类型定义
export interface TranslationKeys {
  common: {
    navigation: {
      home: string;
      projects: string;
      tasks: string;
      team: string;
      chat: string;
      analytics: string;
      settings: string;
      tracker: string;
      perks: string;
      expenses: string;
      cap_table: string;
      shareholders: string;
      note_holders: string;
      transactions_log: string;
    };
    common: {
      new: string;
      add: string;
      edit: string;
      delete: string;
      save: string;
      cancel: string;
      confirm: string;
      loading: string;
      error: string;
      success: string;
      warning: string;
      info: string;
      search: string;
      filter: string;
      sort: string;
      refresh: string;
      close: string;
      back: string;
      next: string;
      previous: string;
      submit: string;
      reset: string;
    };
    auth: {
      login: string;
      logout: string;
      signup: string;
      email: string;
      password: string;
      remember_me: string;
      forgot_password: string;
      create_account: string;
      already_have_account: string;
      dont_have_account: string;
    };
  };
  settings: {
    title: string;
    subtitle: string;
    tabs: {
      profile: string;
      appearance: string;
      account: string;
      billing: string;
      team: string;
    };
    account: {
      title: string;
      subtitle: string;
      full_name: string;
      full_name_description: string;
      username: string;
      username_description: string;
      email: string;
      email_description: string;
      language: string;
      language_description: string;
      timezone: string;
      timezone_description: string;
      notifications: string;
      email_notifications: string;
      email_notifications_description: string;
      push_notifications: string;
      push_notifications_description: string;
      marketing_emails: string;
      marketing_emails_description: string;
    };
    languages: {
      zh: string;
      en: string;
      ja: string;
      ko: string;
      fr: string;
      de: string;
      es: string;
    };
  };
}

// 支持的语言类型
export type SupportedLanguage = 'zh' | 'en' | 'ja' | 'ko' | 'fr' | 'de' | 'es';

// 语言选项接口
export interface LanguageOption {
  code: SupportedLanguage;
  name: string;
  flag: string;
}
