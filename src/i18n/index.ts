import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译资源
import zhCommon from '../locales/zh/common.json';
import zhSettings from '../locales/zh/settings.json';
import enCommon from '../locales/en/common.json';
import enSettings from '../locales/en/settings.json';

// 支持的语言列表
export const supportedLanguages = [
  { code: 'zh', name: '中文（简体）', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'ko', name: '한국어', flag: '🇰🇷' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'es', name: '<PERSON>spañol', flag: '🇪🇸' },
];

// 翻译资源
const resources = {
  zh: {
    common: zhCommon,
    settings: zhSettings,
  },
  en: {
    common: enCommon,
    settings: enSettings,
  },
  // 其他语言可以后续添加
  ja: {
    common: enCommon, // 暂时使用英文作为占位符
    settings: enSettings,
  },
  ko: {
    common: enCommon,
    settings: enSettings,
  },
  fr: {
    common: enCommon,
    settings: enSettings,
  },
  de: {
    common: enCommon,
    settings: enSettings,
  },
  es: {
    common: enCommon,
    settings: enSettings,
  },
};

// 语言检测配置
const detectionOptions = {
  // 检测顺序：localStorage -> navigator -> 默认语言
  order: ['localStorage', 'navigator', 'htmlTag'],
  
  // localStorage 键名
  lookupLocalStorage: 'i18nextLng',
  
  // 缓存用户语言选择
  caches: ['localStorage'],
  
  // 排除某些检测方法
  excludeCacheFor: ['cimode'],
  
  // 检查白名单
  checkWhitelist: true,
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    
    // 默认语言
    fallbackLng: 'zh',
    
    // 支持的语言白名单
    supportedLngs: supportedLanguages.map(lang => lang.code),
    
    // 语言检测配置
    detection: detectionOptions,
    
    // 调试模式（生产环境应设为 false）
    debug: import.meta.env.DEV,
    
    // 默认命名空间
    defaultNS: 'common',
    
    // 命名空间
    ns: ['common', 'settings'],
    
    // 插值配置
    interpolation: {
      escapeValue: false, // React 已经处理了 XSS
    },
    
    // React 配置
    react: {
      useSuspense: false, // 禁用 Suspense，避免加载问题
    },
    
    // 键分隔符
    keySeparator: '.',
    
    // 命名空间分隔符
    nsSeparator: ':',
  });

export default i18n;
