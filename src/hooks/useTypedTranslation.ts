import { useTranslation as useI18nTranslation } from 'react-i18next';
import type { TFunction } from 'react-i18next';

// 类型安全的翻译 hook
export const useTypedTranslation = <T extends string = 'common'>(namespace?: T) => {
  const result = useI18nTranslation(namespace);
  
  return {
    ...result,
    t: result.t as TFunction<T>,
  };
};

// 导出默认的 useTranslation 以保持兼容性
export { useTranslation } from 'react-i18next';
