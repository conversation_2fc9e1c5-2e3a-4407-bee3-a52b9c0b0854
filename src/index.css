@import "tailwindcss";
@plugin './hero.ts';
/* Note: You may need to change the path to fit your project structure */
@source '../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));

/* 全屏显示重置样式 */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 修复 backdrop-filter 支持 */
@supports (backdrop-filter: blur(0)) {
  .backdrop-blur-md {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  .backdrop-saturate-150 {
    backdrop-filter: saturate(1.5);
    -webkit-backdrop-filter: saturate(1.5);
  }

  .backdrop-blur-md.backdrop-saturate-150 {
    backdrop-filter: blur(12px) saturate(1.5);
    -webkit-backdrop-filter: blur(12px) saturate(1.5);
  }
}