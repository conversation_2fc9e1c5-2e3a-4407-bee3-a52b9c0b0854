import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useState } from 'react';
import LoginForm from './components/LoginForm';
import SignUpForm from './components/SignUpForm';
import SidebarLayout from './components/SidebarLayout';
import HomePage from './components/HomePage';
import ProjectsPage from './components/ProjectsPage';
import TasksPage from './components/TasksPage';
import TeamPage from './components/TeamPage';
import AnalyticsPage from './components/AnalyticsPage';
import MessagingApp from './components/messaging/MessagingApp';
import SettingsPage from './components/settings/SettingsPage';

// 初始化 i18n
import './i18n';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // 从localStorage读取认证状态
    return localStorage.getItem('isAuthenticated') === 'true';
  });

  // 模拟登录状态，实际项目中应该从认证系统获取
  const handleLogin = () => {
    setIsAuthenticated(true);
    localStorage.setItem('isAuthenticated', 'true');
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
  };

  if (!isAuthenticated) {
    return (
      <Router>
        <Routes>
          <Route path="/login" element={<LoginForm onLogin={handleLogin} />} />
          <Route path="/signup" element={<SignUpForm />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </Router>
    );
  }

  return (
    <Router>
      <Routes>
        <Route path="/messaging" element={
          <SidebarLayout showHeader={false}>
            <MessagingApp />
          </SidebarLayout>
        } />
        <Route path="/*" element={
          <SidebarLayout>
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/home" element={<HomePage />} />
              <Route path="/projects" element={<ProjectsPage />} />
              <Route path="/tasks" element={<TasksPage />} />
              <Route path="/team" element={<TeamPage />} />
              <Route path="/analytics" element={<AnalyticsPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </SidebarLayout>
        } />
      </Routes>
    </Router>
  );
}

export default App;
