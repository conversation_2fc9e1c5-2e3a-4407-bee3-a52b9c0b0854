import { test, expect } from '@playwright/test';

test.describe('Settings Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app and login first
    await page.goto('http://localhost:5174');
    
    // Check if we need to login
    const loginButton = page.locator('button:has-text("登录")');
    if (await loginButton.isVisible()) {
      await loginButton.click();
    }
    
    // Navigate to settings page
    await page.goto('http://localhost:5174/settings');
  });

  test('should display settings page title and description', async ({ page }) => {
    await expect(page.locator('h1:has-text("Settings")')).toBeVisible();
    await expect(page.locator('text=Customize settings, email preferences, and web appearance.')).toBeVisible();
  });

  test('should display all setting tabs', async ({ page }) => {
    await expect(page.locator('[role="tab"]:has-text("Profile")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Appearance")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Account")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Billing")')).toBeVisible();
    await expect(page.locator('[role="tab"]:has-text("Team")')).toBeVisible();
  });

  test('should switch between tabs correctly', async ({ page }) => {
    // Profile tab should be active by default
    await expect(page.locator('text=This displays your public profile on the site.')).toBeVisible();
    
    // Click on Appearance tab
    await page.locator('[role="tab"]:has-text("Appearance")').click();
    await expect(page.locator('text=Change the appearance of the web.')).toBeVisible();
    
    // Click on Account tab
    await page.locator('[role="tab"]:has-text("Account")').click();
    await expect(page.locator('text=Name to be used for emails.')).toBeVisible();
    
    // Click on Billing tab
    await page.locator('[role="tab"]:has-text("Billing")').click();
    await expect(page.locator('text=Payment method')).toBeVisible();
    
    // Click on Team tab
    await page.locator('[role="tab"]:has-text("Team")').click();
    await expect(page.locator('text=Manage and invite Team Members.')).toBeVisible();
  });

  test('should interact with profile settings', async ({ page }) => {
    // Should be on Profile tab by default
    await expect(page.locator('text=This displays your public profile on the site.')).toBeVisible();
    
    // Test input fields
    const titleInput = page.locator('input[placeholder*="Customer Support"]');
    await titleInput.fill('Senior Developer');
    await expect(titleInput).toHaveValue('Senior Developer');
    
    const locationInput = page.locator('input[placeholder*="Buenos Aires"]');
    await locationInput.fill('New York, USA');
    await expect(locationInput).toHaveValue('New York, USA');
    
    // Test update button
    await expect(page.locator('button:has-text("Update Profile")')).toBeVisible();
  });

  test('should interact with appearance settings', async ({ page }) => {
    // Click on Appearance tab
    await page.locator('[role="tab"]:has-text("Appearance")').click();
    
    // Test theme selection
    await expect(page.locator('text=Light')).toBeVisible();
    await expect(page.locator('text=Dark')).toBeVisible();
    
    // Test font size dropdown
    const fontSizeSelect = page.locator('[aria-label*="font size"]').first();
    if (await fontSizeSelect.isVisible()) {
      await fontSizeSelect.click();
      await expect(page.locator('text=Small')).toBeVisible();
      await expect(page.locator('text=Medium')).toBeVisible();
      await expect(page.locator('text=Large')).toBeVisible();
    }
    
    // Test switches
    await expect(page.locator('text=Translucent UI')).toBeVisible();
    await expect(page.locator('text=Use pointer cursor')).toBeVisible();
  });

  test('should interact with team settings', async ({ page }) => {
    // Click on Team tab
    await page.locator('[role="tab"]:has-text("Team")').click();
    
    // Test invite functionality
    await expect(page.locator('text=Invite new members by email address')).toBeVisible();
    
    const emailInput = page.locator('input[placeholder*="<EMAIL>"]');
    await emailInput.fill('<EMAIL>');
    await expect(emailInput).toHaveValue('<EMAIL>');
    
    // Test role selection
    const roleSelect = page.locator('button:has-text("Member")').first();
    if (await roleSelect.isVisible()) {
      await roleSelect.click();
      await expect(page.locator('text=Admin')).toBeVisible();
    }
    
    // Test team table
    await expect(page.locator('text=Tony Reichert')).toBeVisible();
    await expect(page.locator('text=Zoey Lang')).toBeVisible();
  });
});
